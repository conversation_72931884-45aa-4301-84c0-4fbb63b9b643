import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { MusicProvider } from "@/components/MusicContext";
import FloatingMusicButton from "@/components/FloatingMusicButton";
import YouTubeMusicPlayer from "@/components/YouTubeMusicPlayer";
import MusicReactiveBackground from "@/components/MusicReactiveBackground";
import MusicReactiveCursor from "@/components/MusicReactiveCursor";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Arkit_k - Portfolio",
  description: "Software engineer, technical writer & open-source maintainer",
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
    other: [
      {
        rel: 'android-chrome-192x192',
        url: '/android-chrome-192x192.png',
      },
      {
        rel: 'android-chrome-512x512',
        url: '/android-chrome-512x512.png',
      },
    ],
  },
  manifest: '/site.webmanifest',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <MusicProvider>
            <MusicReactiveBackground />
            <MusicReactiveCursor />
            {children}
            {/* Desktop Music Players */}
            <div className="hidden md:block">
              <FloatingMusicButton
                playlistId="37i9dQZF1DXcBWIGoYBM5M"
                playlistName="🎵 Arkit's Favorite Vibes"
                delay={3000}
              />
              <YouTubeMusicPlayer
                videoId="jfKfPfyJRdk"
                title="Arkit's Coding Vibes"
                autoShow={true}
              />
            </div>
          </MusicProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
